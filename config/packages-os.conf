#################################
# OS Package Configuration file #
#################################

# python image utilizes debian:bullseye
# OS packages are installed via apt-get. To add a new package, enter the package name on a newline
# at the approriate location (packages are sorted alphabetically).
wget
zip
build-essential
zlib1g-dev
libgdbm-dev
libnss3-dev
libssl-dev
libreadline-dev
libffi-dev
libexpat1-dev

# Requirements for Simba Apache Spark ODBC Connector
libsasl2-modules-gssapi-mit
unixodbc
unixodbc-dev