from setuptools import find_packages, setup

setup(
    name="storm-restoration-databricks-ingest",
    version="0.2",
    description="Project for ingesting live outages from databricks to mongo.",
    author="E Source Data Science",
    author_email="<EMAIL>",
    packages=find_packages(
        exclude=[
            "test",
            "test*",
            "Dockerfile",
            "Jenkinsfile",
            ".gitignore",
        ]
    ),
    install_requires=[
        "boto3",
        "pandas==1.5.2",
        "s3fs",
        "pyspark",
        "databricks"
    ],
    python_requires=">=3.9",
)
