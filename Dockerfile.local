FROM python:3.11-slim

# Set environment variables
ENV SED_FILE_PARSE_REGEX='/^\s*#/d;/^\s*$/d;s/[\r\n]//g'
ENV BUILD_CONFIG_DIR='config'

# Set working directory
WORKDIR /root

# Copy package configuration
COPY config/packages-os.conf packages-os.conf

# Install curl and unzip for AWS CLI
RUN apt-get update && \
    apt-get install -y curl unzip && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Install AWS CLI
RUN curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip" && \
    unzip awscliv2.zip && \
    ./aws/install && \
    rm -rf awscliv2.zip aws

# Update, upgrade, install required packages, and clean up
RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get install -y $(sed $SED_FILE_PARSE_REGEX packages-os.conf) && \
    # Fix CVE-2023-52425 by installing libexpat 2.6.0 from Debian Trixie
    echo "deb http://deb.debian.org/debian trixie main" >> /etc/apt/sources.list && \
    apt-get update && \
    apt-get install -y -t trixie libexpat1 libexpat1-dev && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Install Databricks ODBC driver
RUN wget https://databricks-bi-artifacts.s3.us-east-2.amazonaws.com/simbaspark-drivers/odbc/2.6.26/SimbaSparkODBC-2.6.26.1045-Debian-64bit.zip && \
    unzip SimbaSparkODBC-2.6.26.1045-Debian-64bit.zip && \
    dpkg -i simbaspark_2.6.26.1045-2_amd64.deb && \
    rm -rf SimbaSparkODBC-2.6.26.1045-Debian-64bit.zip simbaspark_2.6.26.1045-2_amd64.deb

# Copy ODBC configuration files
COPY config/odbc.ini /etc/odbc.ini
RUN chmod 777 /etc/odbc.ini

COPY config/odbcinst.ini /etc/odbcinst.ini

# Create user and group
RUN groupadd -g 999 docker-users \
  && useradd -r -u 999 -g docker-users storm-etl

# Create home directory
RUN mkdir -m 755 -p /home/<USER>
  && chown storm-etl:docker-users /home/<USER>

# Switch to storm-etl user directory
WORKDIR /home/<USER>

# Create virtual environment and upgrade pip
RUN python -m venv /home/<USER>/venv \
  && python -m pip install --upgrade pip

# Install common Python packages (without private packages)
RUN pip install \
    pandas \
    pymongo \
    boto3 \
    requests \
    pyodbc \
    databricks-sql-connector

# Copy source code
COPY --chown=storm-etl:docker-users src/ src/

# Set the user
USER storm-etl

# Set environment variables for the application
ENV PYTHONPATH="/home/<USER>/src:$PYTHONPATH"

# Default command
CMD ["python", "-m", "src.main"]
