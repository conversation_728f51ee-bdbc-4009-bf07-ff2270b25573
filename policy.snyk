# Snyk (https://snyk.io) policy file, patches or ignores known vulnerabilities.
version: v1.25.0
ignore:
  SNYK-DEBIAN12-ZLIB-6008963:
    - '*':
        reason: No fix available 
        expires: '2025-12-01T00:00:00.000Z'
  SNYK-JAVA-COMMONSLANG-10734077:
    - '*':
        reason: No fix available 
        expires: '2025-12-01T00:00:00.000Z'
  SNYK-JAVA-IONETTY-11799531:
    - '*':
      reason: Waiting for Spring Boot to update Netty
      expires: 2025-12-31T00:00:00.000Z
  SNYK-JAVA-ORGAPACHETOMCATEMBED-11799152:
    - '*':
      reason: Waiting for Spring Boot to update Tom<PERSON>
      expires: 2025-12-31T00:00:00.000Z
  SNYK-JAVA-ORGSPRINGFRAMEWORK-12008931:
    - '*':
      reason: No patched version of spring-boot-starter-web includes fixed spring-beans
      expires: 2025-12-31T00:00:00.000Z
  SNYK-DEBIAN12-SQLITE3-11191064:
    - '*':
      reason: <PERSON> requested it be ignored, <PERSON> approved
      expires: 2025-12-31T00:00:00.000<PERSON>