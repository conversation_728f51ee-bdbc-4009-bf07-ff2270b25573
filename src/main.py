import logging
import os

from datetime import datetime
import json
import pandas as pd
import time
from bson import ObjectId
from databricks import sql
import os
import logging
from botocore.config import Config
from ETRWriter.Writer import ETRSessionBuilder, ActiveOutagesDocumentBuilder
from ETRWriter.ETR import ActiveOutageRecord, ModelFeatsDocument
import pandas as pd
from datetime import datetime, timezone
from bson import ObjectId
from rabbitmq_writer.rabbitmq_client import RabbitMQClient
# --------------------
# ENVIRONMENT
# --------------------

# Logging Setup
logging.basicConfig(format="%(asctime)s %(levelname)s [%(name)s] %(message)s")
logging.getLogger().setLevel(os.environ.get("LOG_LEVEL", logging.INFO))

# Client Auth
CLIENT_AUTHORIZATION = os.getenv("CLIENT_AUTHORIZATION")

# Mongo Creds
MONGO_USER = os.getenv("MONGO_USER")
MONGO_PASSWORD = os.getenv("MONGO_PASSWORD")
MONGO_HOST = os.getenv("MONGO_HOST")

# Auth Control Flow
AWS_ACCESS_TOKEN = os.getenv("AWS_ACCESS_TOKEN")

# Databricks
DBS_HOST = os.getenv("DBS_HOST")
DBS_HTTP_PATH = os.getenv("DBS_HTTP_PATH")

# Queries
DBS_CATALOG = os.getenv("DBS_CATALOG")
DBS_SCHEMA = os.getenv("DBS_SCHEMA")
DBS_ACTIVE_OUTAGES_TABLE = os.getenv("DBS_ACTIVE_OUTAGES_TABLE", "scraped_active_outages")
#  client_pcg_prod.gold.scraped_active_outages

#RabbitMQ
RABBITMQ_EXCHANGE = os.getenv("RABBITMQ_EXCHANGE")
RABBITMQ_ROUTING_KEY = os.getenv("RABBITMQ_ROUTING_KEY")

activeOutageId = ObjectId()

# --------------------
# Classes
# --------------------
# --------------------
# Functions
# --------------------
def df_size(df: pd.DataFrame):
    return df.shape[0]

def run_dbs_query(cursor, query: str):
    df = pd.DataFrame()

    try:
        logging.info(f"Running dbx query: '{query}'...")
        cursor.execute(query)
        df = pd.DataFrame.from_records(
            cursor.fetchall(), columns=[col[0] for col in cursor.description]
        )

        if not df.empty:
            logging.info(f"Query returned {df_size(df)} record(s).")
        else:
            logging.warning("Query returned no results.")

    except Exception as e:
        logging.error(e)

    return df

######################
### Active Outages ###
######################
def get_active_outages_df():
    active_outages_query = f"""
    SELECT * 
    FROM {DBS_CATALOG}.{DBS_SCHEMA}.{DBS_ACTIVE_OUTAGES_TABLE};
    """  # noqa

    try:
        logging.info(f"Getting active outages data from {DBS_CATALOG}.{DBS_SCHEMA}.{DBS_ACTIVE_OUTAGES_TABLE} ...")
        df = run_dbs_query(cursor, active_outages_query)

        if df.empty:
            logging.info(
                "The Active Outages DataFrame is empty or not found. Skipping processing!"
            )
        else:
            print(df)
            process_active_outages(df)

    except Exception as e:
        logging.error(f"Error running query: {e}")
        
def process_active_outages(df):
    logging.info("Processing Active Outages")
    
    write_active_outages_to_mongo(df)
    
def write_active_outages_to_mongo(df):
    logging.info("Writing Active Outages")
    
    formatted_current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    #lastUpdatedTime = datetime.now() #they don't have a datapoint for this in dbx. Maybe they should add in future?
    outage_records = get_outage_list(df)
    model_feats = get_modelFeats_list(df)

    activeOutageId = ObjectId()

    session = ETRSessionBuilder(sessionType = "active_outage").build()
    with session as s:
        s.add_document(
            ActiveOutagesDocumentBuilder(
                id=activeOutageId,
                session=s.id,
                outageRecords=outage_records,
                modelFeats=model_feats,
                lastUpdated=formatted_current_time,
                createdTimestamp=formatted_current_time,
                generatedBy="system",
            ).build()
        )
    logging.info("Writing Outages complete!")

def get_outage_list(df):
    return df.apply(lambda row: ActiveOutageRecord(
        region=row['territoryName'].title(), #check for edge cases
        activeCustomerOutages=row['activeCustomerOutages'],
        activeIncidents=row['activeIncidents'],
        restoredCustomerOutages=row['restoredCustomerOutages'],
        restoredIncidents=row['restoredIncidents']
    ), axis=1).tolist()
    
def get_modelFeats_list(df):
    try:
        required_columns = [
            'territoryName',
            'activeNotAssessedIncidents',
            'activeOnSiteIncidents',
            'restoredIncidents_hour',
            'restoredCustomerOutages_hour',
            'mean_miles_from_middle',
            'activeCustomerOutages_lag_1hour',
            'activeCustomerOutages_lag_2hour',
            'activeCustomerOutages_lag_3hour',
            'activeCustomerOutages_lag_4hour',
            'activeCustomerOutages_lag_5hour'
        ]
        
        for col in required_columns:
            if col not in df.columns:
                raise ValueError(f"Missing required column: {col}")

        return df.apply(lambda row: ModelFeatsDocument(
            region=row['territoryName'].title(),
            activeNotAssessedIncidents=int(row['activeNotAssessedIncidents']),
            activeOnSiteIncidents=int(row['activeOnSiteIncidents']),
            restoredIncidentsHour=int(row['restoredIncidents_hour']),
            restoredCustomerOutagesHour=int(row['restoredCustomerOutages_hour']),
            meanMilesFromMiddle=float(row['mean_miles_from_middle']),
            activeCustomerOutagesLag1Hour=int(row['activeCustomerOutages_lag_1hour']),
            activeCustomerOutagesLag2Hour=int(row['activeCustomerOutages_lag_2hour']),
            activeCustomerOutagesLag3Hour=int(row['activeCustomerOutages_lag_3hour']),
            activeCustomerOutagesLag4Hour=int(row['activeCustomerOutages_lag_4hour']),
            activeCustomerOutagesLag5Hour=int(row['activeCustomerOutages_lag_5hour'])
        ), axis=1).tolist()
    except ValueError as ve:
        print(f"Value error: {ve}")
        return []
    except TypeError as te:
        print(f"Type error: {te}")
        return []
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return []
    
def to_rfc3339_string(dt):
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=timezone.utc)

    return dt.isoformat()

def publish_event():    
    client = RabbitMQClient()
    client.send_message(
        json.dumps(build_aggregate_overview_event()),
        routing_key=RABBITMQ_ROUTING_KEY,
        exchange=RABBITMQ_EXCHANGE,
    )
    client.close_connection()

def build_aggregate_overview_event(): 
    aggregate_overview_event = {
        "eventName": "aggregateOverviewEvent",
        "timestamp": to_rfc3339_string(datetime.now()),
        "metadata": {
            "activeOutageId": str(activeOutageId),
        },
    }

    return aggregate_overview_event
    


def run():
    while True:
        try:
            time.sleep(60)  # Simulate some work
        except Exception as e:
            logging.error(f"An error occurred: {e}")

## Interactive pod work ONLY! ##
# run()

##################
### Main block ###
##################
logging.info("Connecting to Databricks in AWS...")

try:
    with sql.connect(
        server_hostname=DBS_HOST,
        http_path=DBS_HTTP_PATH,
        access_token=AWS_ACCESS_TOKEN,
    ) as conn:
        logging.info("Connection to AWS Databricks successful!")

        with conn.cursor() as cursor:
            
            get_active_outages_df()
            
            logging.info("Running Aggregate Overview Metric Rabbit Publisher...")
            try: 
                publish_event()
            except Exception as e:
                logging.error("Error: Running Aggregate Overview Metric Rabbit Publisher...", e)
                pass
            
            logging.info("Job Complete!")

except Exception as e:
    logging.error(f"Error connecting to AWS Databricks: {e}")