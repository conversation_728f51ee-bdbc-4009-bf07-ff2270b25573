FROM python:3.11-slim-trixie

ARG NRU=storm-etl
ARG NRUG=docker-users
ARG CODE_ARTIFACT_TOKEN

ENV HOME=/home/<USER>
ENV VIRTUAL_ENV=$HOME/venv
ENV PATH=$VIRTUAL_ENV/bin:$PATH

ARG SED_FILE_PARSE_REGEX='/^\s*#/d;/^\s*$/d;s/[\r\n]//g'

ARG BUILD_CONFIG_DIR=config
ARG BUILD_SOURCE_DIR=src

WORKDIR /root

# Install OS packages
# --------------------------------------------------
COPY $BUILD_CONFIG_DIR/packages-os.conf packages-os.conf

# Install curl and unzip (required to download and extract AWS CLI)
RUN apt-get update && \
  apt-get install -y curl unzip && \
  # Clean up cache to reduce layer size
  apt-get clean && \
  rm -rf /var/lib/apt/lists/*

# Download, unzip, install, and clean up AWS CLI installation
RUN curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip" && \
    unzip awscliv2.zip && \
    ./aws/install && \
    rm -rf aws awscliv2.zip

# Update, upgrade, install required packages, and clean up
RUN apt-get update && \
    apt-get upgrade -y && \
    # Add Debian Trixie repository for libexpat fix (CVE-2023-52425)
    echo "deb http://deb.debian.org/debian trixie main" >> /etc/apt/sources.list && \
    apt-get update && \
    # Install OS packages (this will install vulnerable libexpat 2.5.0 from bookworm)
    apt-get install -y $(sed $SED_FILE_PARSE_REGEX packages-os.conf) && \
    # Override with fixed libexpat 2.7.1+ from Debian Trixie to fix CVE-2023-52425
    apt-get install -y -t trixie libexpat1 libexpat1-dev && \
    # Verify the fix was applied
    dpkg -l | grep libexpat && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Install ODBC Simba Driver
# --------------------
RUN wget https://databricks-bi-artifacts.s3.us-east-2.amazonaws.com/simbaspark-drivers/odbc/2.6.26/SimbaSparkODBC-2.6.26.1045-Debian-64bit.zip \
  && unzip SimbaSparkODBC-2.6.26.1045-Debian-64bit.zip \
  && rm SimbaSparkODBC-2.6.26.1045-Debian-64bit.zip \
  && dpkg -i simbaspark_2.6.26.1045-2_amd64.deb \
  && rm simbaspark_2.6.26.1045-2_amd64.deb

COPY $BUILD_CONFIG_DIR/odbc.ini /etc/odbc.ini
# Script needs to write access code into this file. So it needs to be open.
RUN chmod 777 /etc/odbc.ini
COPY $BUILD_CONFIG_DIR/odbcinst.ini /etc/odbcinst.ini

# Configure non-root user, group, and home directory
# --------------------------------------------------
RUN addgroup --system "${NRUG}" || true \
  && adduser  --system --ingroup "${NRUG}" --home "/home/<USER>" "${NRU}"
RUN mkdir -m 755 -p $HOME && chown $NRU:$NRUG $HOME

# Switch to NRU and set working directory
# ---------------------------------------
USER $NRU
WORKDIR $HOME

# Configure python VENV with proper libraries
# -------------------------------------------
RUN python -m venv $VIRTUAL_ENV \
  && python -m pip install --upgrade pip
COPY --chown=$NRU:$NRUG $BUILD_CONFIG_DIR/packages-py.conf $BUILD_CONFIG_DIR/packages-py.conf

USER root
RUN --mount=type=secret,id=CODEARTIFACT,dst=/run/secrets/codeartifact pip config set global.extra-index-url https://aws:"$(cat /run/secrets/codeartifact)"@esource-int-artifacts-411985166407.d.codeartifact.us-east-1.amazonaws.com/pypi/pypi/simple/ && \
    pip install --no-cache-dir $(sed $SED_FILE_PARSE_REGEX $BUILD_CONFIG_DIR/packages-py.conf)
USER $NRU

# Copy in source code
# -------------------------------------------
COPY --chown=$NRU:$NRUG $BUILD_SOURCE_DIR/main.py $BUILD_SOURCE_DIR/main.py

USER root
RUN mkdir -p /scripts && \
    chown $NRU:$NRUG /scripts

USER $NRU

ENTRYPOINT ["/bin/bash"]